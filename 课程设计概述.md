# 1. 课程设计概述

## 1.1 问题描述

### 目的
本课程设计旨在通过实现最小生成树算法，加深对图论算法的理解，掌握贪心算法的设计思想，并提高编程实践能力。通过对比Kruskal算法和Prim算法的实现过程和性能特点，培养学生分析和解决实际问题的能力。

### 内容
最小生成树问题是图论中的经典问题之一，在实际应用中具有重要意义。若要在n个城市之间建设通信网络，只需要建设n-1条线路即可实现全网连通。如何以最低的经济代价建设这个通信网，就是一个网的最小生成树问题。

在一个加权无向连通图中，最小生成树是指包含图中所有顶点的树，且所有边的权值之和最小。该问题在通信网络建设、电力网络规划、交通路线设计等领域都需要寻找成本最低的连接方案。

本课程设计实现两种经典的最小生成树算法：

**Kruskal算法**：这是一种基于边的贪心算法，其基本思想是将图中的所有边按权值从小到大排序，然后依次选择权值最小的边加入生成树，但要保证不形成环路。该算法使用并查集数据结构来检测环的形成，时间复杂度为O(E log E)，其中E为边数。

**Prim算法**：这是一种基于顶点的贪心算法，其基本思想是从任意一个顶点开始，逐步扩展最小生成树。每次选择与当前生成树相邻的权值最小的边，将对应的顶点加入生成树，直到包含所有顶点。该算法的时间复杂度为O(V²)，其中V为顶点数。

### 题目
通信线路一旦建成，必然是双向的。因此，构造最小生成树的网一定是无向网。本课程设计要求实现最小生成树问题的求解，具体包括使用Kruskal算法和Prim算法两种方法来构建最小生成树，并输出生成树的各条边及其权值。

## 1.2 基本要求

本课程设计需要完成以下功能和性能要求：

(1) **实现Kruskal算法求最小生成树**
   - 对图中所有边按权值进行排序
   - 使用并查集数据结构检测环路
   - 逐步选择权值最小且不形成环的边
   - 输出算法执行过程和最终结果

(2) **实现Prim算法求最小生成树**
   - 从指定顶点开始构建生成树
   - 维护顶点到生成树的最小距离
   - 逐步选择距离最小的顶点加入生成树
   - 输出算法执行过程和最终结果

(3) **实现图的数据输入与存储功能**
   - 支持手动输入图的顶点和边信息
   - 支持随机生成测试图数据
   - 采用边数组和邻接矩阵混合存储结构
   - 确保生成的图是连通的无向图

(4) **实现测试数据的生成与验证**
   - 利用C语言随机函数生成权值小于100的整数
   - 支持顶点数不超过30个的图
   - 生成多批测试数据验证算法正确性
   - 对比两种算法结果的一致性

(5) **实现完整的结果输出功能**
   - 输出原图的所有边及权值信息
   - 输出两种算法的详细执行过程
   - 输出最小生成树的各条边及权值
   - 输出最小生成树的总权值

(6) **系统界面友好，风格统一易操作**
   - 提供清晰的菜单选项和操作提示
   - 支持多种数据输入方式
   - 输出格式规范，便于结果分析

(7) **系统运行稳定，可以完成异常数据的检测和处理，运行结果正确可靠**
   - 对输入数据进行有效性检查
   - 处理顶点编号超出范围等异常情况
   - 确保算法逻辑正确，结果可靠

(8) **系统有较好的时间和空间效率**
   - Kruskal算法时间复杂度为O(E log E)
   - Prim算法时间复杂度为O(V²)
   - 合理使用内存，避免不必要的空间浪费

(9) **文档结构合理，格式规范，图表清晰**
   - 提供完整的设计文档和用户手册
   - 包含算法流程图和数据结构设计
   - 测试结果展示清晰，分析深入

## 1.3 开发环境

**硬件环境：**
- 处理器：Intel/AMD x64架构处理器
- 内存：4GB以上
- 存储：至少100MB可用磁盘空间

**软件环境：**
- 操作系统：Windows 10/11
- 编程语言：C语言（符合C99标准）
- 开发工具：
  - 编译器：GCC (GNU Compiler Collection) 或 Microsoft Visual C++
  - 集成开发环境：Dev-C++、Code::Blocks、Visual Studio Code等
  - 调试工具：GDB调试器
  - 版本控制：Git

**开发标准：**
- 编程规范：遵循C语言编程规范
- 代码风格：统一的命名规范和注释风格
- 文档标准：按照课程设计报告模板要求

**测试环境：**
- 支持控制台应用程序运行
- 能够处理标准输入输出操作
- 支持文件读写操作（用于测试数据）
